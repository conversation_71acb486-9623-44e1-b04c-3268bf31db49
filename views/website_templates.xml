<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!--
        WEBSITE TEMPLATE NOTES:

        Some website templates have been temporarily disabled due to XPath issues.
        These templates try to inherit from Odoo's website layout but the XPath expressions
        cannot locate the target elements in the parent views.

        Disabled Templates:
        1. header_search_bar - XPath: //nav[@class='navbar navbar-expand-md navbar-light']
        2. user_watchlist_menu - XPath: //a[@href='/my']
        3. footer_market_links - XPath: //div[@class='row']

        To fix these later:
        1. Inspect the actual structure of the parent templates
        2. Use browser developer tools to find correct selectors
        3. Test XPath expressions in a development environment
        4. Consider using position="replace" instead of "inside" for some cases

        Alternative approach:
        - Create standalone templates that can be included manually
        - Use JavaScript to inject content dynamically
        - Override entire parent templates instead of using XPath
        -->

        
        <!-- Website Menu Items -->
        <record id="menu_market_explorer" model="website.menu">
            <field name="name">Market</field>
            <field name="url">/market</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">20</field>
        </record>
        
        <!-- Homepage Extension -->
        <template id="homepage_market_section" inherit_id="website.homepage" name="Market Section">
            <xpath expr="//div[@id='wrap']" position="inside">
                <section class="s_banner pt-5 pb-5 bg-light">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-lg-6">
                                <h2 class="display-4 font-weight-bold">Financial Market Data</h2>
                                <p class="lead">Track stocks, cryptocurrencies, forex, and commodities with real-time data and advanced analytics.</p>
                                <div class="mt-4">
                                    <a href="/market" class="btn btn-primary btn-lg mr-3">Explore Markets</a>
                                    <a href="/web/signup" class="btn btn-outline-primary btn-lg">Create Watchlist</a>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="row">
                                    <div class="col-6 mb-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h3 class="text-primary" id="total-symbols">--</h3>
                                                <p class="mb-0">Symbols</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h3 class="text-success" id="market-status">--</h3>
                                                <p class="mb-0">Market Status</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h3 class="text-info" id="top-gainer">--</h3>
                                                <p class="mb-0">Top Gainer</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h3 class="text-warning" id="top-volume">--</h3>
                                                <p class="mb-0">High Volume</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </xpath>
        </template>
        
        <!-- Header Search Bar Component (Standalone) -->
        <template id="header_search_bar_component" name="Header Search Bar Component">
            <div class="navbar-nav ml-auto mr-3">
                <form class="form-inline" id="header-search-form">
                    <div class="input-group">
                        <input type="text" class="form-control" id="header-search" placeholder="Search symbols..." autocomplete="off"/>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fa fa-search"/>
                            </button>
                        </div>
                    </div>
                    <div id="search-results" class="dropdown-menu w-100" style="display: none;"></div>
                </form>
            </div>
        </template>
        
        <!-- User Watchlist Menu - Temporarily disabled due to XPath issues -->
        <!-- TODO: Find correct XPath for portal.user_dropdown structure -->
        <!--
        <template id="user_watchlist_menu" inherit_id="portal.user_dropdown" name="User Watchlist Menu">
            <xpath expr="//a[@href='/my']" position="after">
                <a class="dropdown-item" href="/my/watchlist">
                    <i class="fa fa-star mr-2"/>My Watchlist
                </a>
            </xpath>
        </template>
        -->
        
        <!-- Footer Market Links - Temporarily disabled due to XPath issues -->
        <!-- TODO: Find correct XPath for website.footer_custom structure -->
        <!--
        <template id="footer_market_links" inherit_id="website.footer_custom" name="Footer Market Links">
            <xpath expr="//div[@class='row']" position="inside">
                <div class="col-lg-3 col-md-6">
                    <h5>Markets</h5>
                    <ul class="list-unstyled">
                        <li><a href="/market?symbol_type=stock">Stocks</a></li>
                        <li><a href="/market?symbol_type=crypto">Cryptocurrency</a></li>
                        <li><a href="/market?symbol_type=forex">Forex</a></li>
                        <li><a href="/market?symbol_type=commodity">Commodities</a></li>
                        <li><a href="/market?symbol_type=index">Indices</a></li>
                    </ul>
                </div>
            </xpath>
        </template>
        -->
        
        <!-- Market Status Widget -->
        <template id="market_status_widget" name="Market Status Widget">
            <div class="market-status-widget">
                <div class="d-flex align-items-center">
                    <div class="status-indicator mr-2" id="market-status-indicator"></div>
                    <div>
                        <div class="font-weight-bold" id="market-status-text">Loading...</div>
                        <small class="text-muted" id="market-last-update"></small>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Trending Symbols Widget -->
        <template id="trending_symbols_widget" name="Trending Symbols Widget">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Trending Symbols</h6>
                </div>
                <div class="card-body p-0">
                    <div id="trending-symbols-list">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Price Alert Modal -->
        <template id="price_alert_modal" name="Price Alert Modal">
            <div class="modal fade" id="priceAlertModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Set Price Alert</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&amp;times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="price-alert-form">
                                <div class="form-group">
                                    <label>Symbol</label>
                                    <input type="text" class="form-control" id="alert-symbol" readonly="readonly"/>
                                </div>
                                <div class="form-group">
                                    <label>Current Price</label>
                                    <input type="text" class="form-control" id="alert-current-price" readonly="readonly"/>
                                </div>
                                <div class="form-group">
                                    <label for="alert-price-above">Alert when price goes above</label>
                                    <input type="number" class="form-control" id="alert-price-above" step="0.01"/>
                                </div>
                                <div class="form-group">
                                    <label for="alert-price-below">Alert when price goes below</label>
                                    <input type="number" class="form-control" id="alert-price-below" step="0.01"/>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="save-price-alert">Save Alert</button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Loading Skeleton -->
        <template id="loading_skeleton" name="Loading Skeleton">
            <div class="loading-skeleton">
                <div class="skeleton-line mb-2" style="width: 60%;"></div>
                <div class="skeleton-line mb-2" style="width: 80%;"></div>
                <div class="skeleton-line mb-2" style="width: 40%;"></div>
                <div class="skeleton-line" style="width: 70%;"></div>
            </div>
        </template>
        
        <!-- Error Message Template -->
        <template id="error_message_template" name="Error Message Template">
            <div class="alert alert-danger" role="alert">
                <h6 class="alert-heading">Error</h6>
                <p class="mb-0" id="error-message-text">An error occurred while loading data.</p>
            </div>
        </template>
        
        <!-- Success Message Template -->
        <template id="success_message_template" name="Success Message Template">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <span id="success-message-text">Operation completed successfully.</span>
                <button type="button" class="close" data-dismiss="alert">
                    <span>&amp;times;</span>
                </button>
            </div>
        </template>
        
        <!-- News Article Card Template -->
        <template id="news_article_card" name="News Article Card">
            <div class="card mb-3 news-article-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0">
                            <a href="#" class="news-title text-decoration-none" target="_blank"></a>
                        </h6>
                        <div class="d-flex">
                            <span class="badge badge-info news-category mr-1"></span>
                            <span class="badge news-sentiment"></span>
                        </div>
                    </div>
                    <p class="card-text news-summary"></p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <span class="news-source"></span> • <span class="news-published-at"></span>
                        </small>
                        <div class="news-impact-level"></div>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Technical Indicator Card Template -->
        <template id="technical_indicator_card" name="Technical Indicator Card">
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card technical-indicator-card">
                    <div class="card-body text-center">
                        <h6 class="card-title indicator-name"></h6>
                        <div class="h4 mb-2 indicator-value"></div>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar indicator-signal-bar" role="progressbar"></div>
                        </div>
                        <small class="text-muted indicator-signal"></small>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Event Card Template -->
        <template id="event_card_template" name="Event Card Template">
            <div class="card mb-3 event-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0 event-title"></h6>
                        <span class="badge event-impact-badge"></span>
                    </div>
                    <p class="card-text event-description"></p>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fa fa-calendar mr-1"></i>
                            <span class="event-date"></span>
                        </small>
                        <span class="badge event-type-badge"></span>
                    </div>
                </div>
            </div>
        </template>
        
    </data>
</odoo>
