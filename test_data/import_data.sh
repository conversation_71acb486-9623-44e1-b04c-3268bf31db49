#!/bin/bash

# TradingView Clone Module - Quick Test Data Import Script
# This script imports all test data files in the correct order

echo "🚀 Starting TradingView Clone test data import..."

# Configuration
ODOO_PATH="/home/<USER>/odooProjects/odoo17/odoo"
DB_NAME="odoo17_tradingview_clone"
ADDONS_PATH="/home/<USER>/odooProjects/odoo17/odoo/addons,/home/<USER>/odooProjects/odoo17/custom_addons_develop_myself"
MODULE_PATH="/home/<USER>/odooProjects/odoo17/custom_addons_develop_myself/tradingview_clone"

# Change to Odoo directory
cd "$ODOO_PATH" || exit 1

echo "📊 Importing test data files..."

# Import data files in correct order
echo "1/7 Importing Financial Symbols..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/01_symbols_data.xml')
env.cr.commit()
print("✅ Symbols imported successfully")
EOF

echo "2/7 Importing OHLC Data..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/02_ohlc_data.xml')
env.cr.commit()
print("✅ OHLC data imported successfully")
EOF

echo "3/7 Importing Technical Indicators..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/03_technical_indicators.xml')
env.cr.commit()
print("✅ Technical indicators imported successfully")
EOF

echo "4/7 Importing News Articles..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/04_news_data.xml')
env.cr.commit()
print("✅ News articles imported successfully")
EOF

echo "5/7 Importing Financial Events..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/05_events_data.xml')
env.cr.commit()
print("✅ Events imported successfully")
EOF

echo "6/7 Importing Watchlists..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/06_watchlist_data.xml')
env.cr.commit()
print("✅ Watchlists imported successfully")
EOF

echo "7/7 Importing Sync Logs..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
env['ir.model.data'].load_data('tradingview_clone', '$MODULE_PATH/test_data/07_sync_log_data.xml')
env.cr.commit()
print("✅ Sync logs imported successfully")
EOF

echo "🔍 Verifying imported data..."
python odoo-bin shell -d "$DB_NAME" --addons-path="$ADDONS_PATH" << EOF
# Verify data counts
symbol_count = env['tradingview.symbol'].search_count([])
ohlc_count = env['tradingview.ohlc'].search_count([])
tech_count = env['tradingview.technical'].search_count([])
news_count = env['tradingview.news'].search_count([])
event_count = env['tradingview.event'].search_count([])
watchlist_count = env['tradingview.watchlist'].search_count([])
sync_log_count = env['tradingview.sync_log'].search_count([])

print(f"📈 Data Import Summary:")
print(f"   Symbols: {symbol_count}")
print(f"   OHLC Records: {ohlc_count}")
print(f"   Technical Indicators: {tech_count}")
print(f"   News Articles: {news_count}")
print(f"   Events: {event_count}")
print(f"   Watchlists: {watchlist_count}")
print(f"   Sync Logs: {sync_log_count}")

# Test the critical search filters we fixed
recent_syncs = env['tradingview.sync_log'].search([('is_recent', '=', True)])
rate_limited_syncs = env['tradingview.sync_log'].search([('rate_limit_hit', '=', True)])
error_syncs = env['tradingview.sync_log'].search([('error_message', '!=', False)])

print(f"🔧 Search Filter Tests:")
print(f"   Recent syncs (last 24h): {len(recent_syncs)}")
print(f"   Rate limited syncs: {len(rate_limited_syncs)}")
print(f"   Syncs with errors: {len(error_syncs)}")

if len(recent_syncs) > 0 and len(rate_limited_syncs) > 0 and len(error_syncs) > 0:
    print("✅ All search filters working correctly!")
else:
    print("⚠️  Some search filters may need verification")

print("🎉 Test data import completed successfully!")
EOF

echo ""
echo "🎯 Next Steps:"
echo "1. Access your Odoo instance"
echo "2. Navigate to TradingView → Symbols to see imported data"
echo "3. Test the Sync Logs search filters (Recent, Rate Limited, With Errors)"
echo "4. Explore different views (Tree, Form, Kanban, Graph, Pivot)"
echo "5. Test the website integration at /market"
echo ""
echo "📚 For detailed testing instructions, see test_data/README.md"
