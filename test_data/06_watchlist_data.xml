<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Tech Stocks Watchlist -->
        <record id="watchlist_tech_stocks" model="tradingview.watchlist">
            <field name="name">Tech Giants Portfolio</field>
            <field name="description">Major technology companies with strong growth potential</field>
            <field name="category">technology</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_aapl'), ref('symbol_msft'), ref('symbol_googl'), ref('symbol_nvda'), ref('symbol_meta')])]"/>
            <field name="target_return">15.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Focus on companies leading in AI, cloud computing, and digital transformation</field>
        </record>

        <!-- Cryptocurrency Watchlist -->
        <record id="watchlist_crypto" model="tradingview.watchlist">
            <field name="name">Crypto Leaders</field>
            <field name="description">Top cryptocurrency assets by market capitalization</field>
            <field name="category">cryptocurrency</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_btcusd'), ref('symbol_ethusd'), ref('symbol_adausd'), ref('symbol_solusd'), ref('symbol_dotusd')])]"/>
            <field name="target_return">25.0</field>
            <field name="risk_level">high</field>
            <field name="notes">High-risk, high-reward cryptocurrency investments with strong fundamentals</field>
        </record>

        <!-- Forex Major Pairs Watchlist -->
        <record id="watchlist_forex_majors" model="tradingview.watchlist">
            <field name="name">Forex Major Pairs</field>
            <field name="description">Most liquid and traded currency pairs in the forex market</field>
            <field name="category">forex</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_eurusd'), ref('symbol_gbpusd'), ref('symbol_usdjpy'), ref('symbol_audusd'), ref('symbol_usdcad')])]"/>
            <field name="target_return">8.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Core forex pairs for currency trading and hedging strategies</field>
        </record>

        <!-- Commodities Watchlist -->
        <record id="watchlist_commodities" model="tradingview.watchlist">
            <field name="name">Precious Metals & Energy</field>
            <field name="description">Key commodity assets for portfolio diversification</field>
            <field name="category">commodities</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_gold'), ref('symbol_silver'), ref('symbol_oil'), ref('symbol_natgas'), ref('symbol_copper')])]"/>
            <field name="target_return">12.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Inflation hedge and portfolio diversification through commodity exposure</field>
        </record>

        <!-- Index ETFs Watchlist -->
        <record id="watchlist_index_etfs" model="tradingview.watchlist">
            <field name="name">Market Index ETFs</field>
            <field name="description">Broad market exposure through index-tracking ETFs</field>
            <field name="category">index</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_qqq'), ref('symbol_dji'), ref('symbol_vix')])]"/>
            <field name="target_return">10.0</field>
            <field name="risk_level">low</field>
            <field name="notes">Diversified market exposure with lower risk through index funds</field>
        </record>

        <!-- Growth Stocks Watchlist -->
        <record id="watchlist_growth_stocks" model="tradingview.watchlist">
            <field name="name">High Growth Stocks</field>
            <field name="description">Companies with exceptional growth potential</field>
            <field name="category">growth</field>
            <field name="is_public">False</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_tsla'), ref('symbol_nvda'), ref('symbol_nflx'), ref('symbol_amzn')])]"/>
            <field name="target_return">20.0</field>
            <field name="risk_level">high</field>
            <field name="notes">High-growth companies with disruptive business models and strong market positions</field>
        </record>

        <!-- Dividend Stocks Watchlist -->
        <record id="watchlist_dividend_stocks" model="tradingview.watchlist">
            <field name="name">Dividend Champions</field>
            <field name="description">Reliable dividend-paying stocks for income generation</field>
            <field name="category">dividend</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_aapl'), ref('symbol_msft'), ref('symbol_spy')])]"/>
            <field name="target_return">6.0</field>
            <field name="risk_level">low</field>
            <field name="notes">Stable companies with consistent dividend payments and growth history</field>
        </record>

        <!-- AI & Technology Watchlist -->
        <record id="watchlist_ai_tech" model="tradingview.watchlist">
            <field name="name">AI Revolution</field>
            <field name="description">Companies at the forefront of artificial intelligence development</field>
            <field name="category">technology</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_nvda'), ref('symbol_msft'), ref('symbol_googl'), ref('symbol_meta')])]"/>
            <field name="target_return">18.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Companies leading the AI revolution with significant investments in machine learning and automation</field>
        </record>

        <!-- Safe Haven Assets Watchlist -->
        <record id="watchlist_safe_haven" model="tradingview.watchlist">
            <field name="name">Safe Haven Assets</field>
            <field name="description">Assets that typically perform well during market uncertainty</field>
            <field name="category">defensive</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_gold'), ref('symbol_silver'), ref('symbol_spy'), ref('symbol_vix')])]"/>
            <field name="target_return">5.0</field>
            <field name="risk_level">low</field>
            <field name="notes">Defensive assets for portfolio protection during market volatility and economic uncertainty</field>
        </record>

        <!-- Electric Vehicle Watchlist -->
        <record id="watchlist_ev_stocks" model="tradingview.watchlist">
            <field name="name">Electric Vehicle Revolution</field>
            <field name="description">Companies driving the transition to electric transportation</field>
            <field name="category">automotive</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_tsla')])]"/>
            <field name="target_return">22.0</field>
            <field name="risk_level">high</field>
            <field name="notes">Electric vehicle manufacturers and supporting technology companies</field>
        </record>

        <!-- Streaming & Entertainment Watchlist -->
        <record id="watchlist_streaming" model="tradingview.watchlist">
            <field name="name">Digital Entertainment</field>
            <field name="description">Streaming services and digital entertainment platforms</field>
            <field name="category">entertainment</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_nflx'), ref('symbol_meta')])]"/>
            <field name="target_return">14.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Companies benefiting from the shift to digital entertainment and streaming services</field>
        </record>

        <!-- Cloud Computing Watchlist -->
        <record id="watchlist_cloud" model="tradingview.watchlist">
            <field name="name">Cloud Infrastructure</field>
            <field name="description">Leading cloud computing and infrastructure providers</field>
            <field name="category">technology</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_msft'), ref('symbol_amzn'), ref('symbol_googl')])]"/>
            <field name="target_return">16.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Companies providing cloud infrastructure and software-as-a-service solutions</field>
        </record>

        <!-- Beginner Portfolio Watchlist -->
        <record id="watchlist_beginner" model="tradingview.watchlist">
            <field name="name">Beginner's Portfolio</field>
            <field name="description">Diversified portfolio suitable for new investors</field>
            <field name="category">balanced</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_aapl'), ref('symbol_msft'), ref('symbol_gold'), ref('symbol_btcusd')])]"/>
            <field name="target_return">10.0</field>
            <field name="risk_level">low</field>
            <field name="notes">Balanced portfolio with mix of stocks, index funds, commodities, and crypto for beginners</field>
        </record>

        <!-- Volatility Trading Watchlist -->
        <record id="watchlist_volatility" model="tradingview.watchlist">
            <field name="name">Volatility Traders</field>
            <field name="description">High-volatility assets for active trading strategies</field>
            <field name="category">trading</field>
            <field name="is_public">False</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_tsla'), ref('symbol_btcusd'), ref('symbol_ethusd'), ref('symbol_vix'), ref('symbol_oil')])]"/>
            <field name="target_return">30.0</field>
            <field name="risk_level">high</field>
            <field name="notes">High-volatility assets suitable for experienced traders with strong risk management</field>
        </record>

        <!-- International Exposure Watchlist -->
        <record id="watchlist_international" model="tradingview.watchlist">
            <field name="name">Global Markets</field>
            <field name="description">International exposure through forex and global assets</field>
            <field name="category">international</field>
            <field name="is_public">True</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_eurusd'), ref('symbol_gbpusd'), ref('symbol_usdjpy'), ref('symbol_audusd'), ref('symbol_gold')])]"/>
            <field name="target_return">8.0</field>
            <field name="risk_level">medium</field>
            <field name="notes">Diversification through international currencies and global commodity exposure</field>
        </record>

    </data>
</odoo>
