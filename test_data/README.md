# TradingView Clone Module - Test Data Import Guide

This directory contains comprehensive test data for the TradingView Clone module in Odoo 17. The test data includes realistic financial market data across multiple asset classes and timeframes.

## 📊 Test Data Overview

### 1. **Financial Symbols (01_symbols_data.xml)**
- **30 symbols** across different asset types:
  - **Cryptocurrencies**: BTCUSD, ETHUSD, ADAUSD, SOLUSD, DOTUSD
  - **Stocks**: AAPL, MSFT, GOOGL, TSLA, NVDA, AMZN, META, NFLX
  - **Forex**: EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD
  - **Commodities**: XAUUSD (Gold), XAGUSD (Silver), USOIL, NATGAS, COPPER
  - **Indices**: SPY, QQQ, DJI, VIX

### 2. **OHLC Historical Data (02_ohlc_data.xml)**
- **Multiple timeframes**: 1D (Daily), 1H (Hourly), 15M (15-minute)
- **30-90 days** of historical data per symbol
- **Realistic price movements** following market patterns
- **Volume data** included for all records

### 3. **Technical Indicators (03_technical_indicators.xml)**
- **RSI, MACD, SMA, EMA, Bollinger Bands** for major symbols
- **Buy/Sell/Hold signals** with strength indicators
- **Multiple timeframes** (1D, 1H)
- **Realistic indicator values** based on current market conditions

### 4. **News Articles (04_news_data.xml)**
- **50+ news articles** related to financial symbols
- **Realistic content** covering earnings, product launches, market analysis
- **Sentiment analysis** (positive, negative, neutral)
- **Impact levels** (high, medium, low)
- **Recent publication dates** for testing age calculations

### 5. **Financial Events (05_events_data.xml)**
- **Upcoming events**: Earnings reports, dividend payments, economic announcements
- **Historical events**: Past earnings, Fed meetings, product launches
- **Event types**: earnings, dividend, economic, conference, product_launch
- **Impact levels** and date calculations

### 6. **Watchlists (06_watchlist_data.xml)**
- **15 different watchlists** covering various investment strategies:
  - Tech Giants Portfolio
  - Crypto Leaders
  - Forex Major Pairs
  - Precious Metals & Energy
  - Market Index ETFs
  - High Growth Stocks
  - Dividend Champions
  - AI Revolution
  - Safe Haven Assets
  - And more...

### 7. **Sync Logs (07_sync_log_data.xml)**
- **20+ sync log entries** to test the fixed `is_recent` field
- **Recent syncs** (last 24 hours) - tests the `is_recent` filter
- **Rate limited syncs** - tests the `rate_limit_hit` filter
- **Syncs with errors** - tests the `error_message` filter
- **Various sync types**: price_update, crypto_prices, forex_rates, news_feed, etc.

## 🚀 Import Methods

### Method 1: Using Odoo Interface (Recommended for Beginners)

1. **Access Odoo Developer Mode**:
   - Go to Settings → Activate Developer Mode
   - Or add `?debug=1` to your Odoo URL

2. **Import via Technical Menu**:
   - Go to **Settings → Technical → Database Structure → Import Data**
   - Select the model (e.g., `tradingview.symbol`)
   - Upload the corresponding XML file
   - Click Import

3. **Import Order** (Important):
   ```
   1. 01_symbols_data.xml (Symbols first - other data depends on these)
   2. 02_ohlc_data.xml
   3. 03_technical_indicators.xml
   4. 04_news_data.xml
   5. 05_events_data.xml
   6. 06_watchlist_data.xml
   7. 07_sync_log_data.xml
   ```

### Method 2: Using Odoo Shell (Recommended for Developers)

1. **Open Odoo Shell**:
   ```bash
   cd /home/<USER>/odooProjects/odoo17/odoo
   python odoo-bin shell -d odoo17_tradingview_clone --addons-path=/home/<USER>/odooProjects/odoo17/odoo/addons,/home/<USER>/odooProjects/odoo17/custom_addons_develop_myself
   ```

2. **Import Data Files**:
   ```python
   # Import symbols first
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/01_symbols_data.xml')
   
   # Import OHLC data
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/02_ohlc_data.xml')
   
   # Import technical indicators
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/03_technical_indicators.xml')
   
   # Import news articles
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/04_news_data.xml')
   
   # Import events
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/05_events_data.xml')
   
   # Import watchlists
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/06_watchlist_data.xml')
   
   # Import sync logs
   env['ir.model.data'].load_data('tradingview_clone', 'test_data/07_sync_log_data.xml')
   
   # Commit changes
   env.cr.commit()
   ```

### Method 3: Using the Import Script

1. **Run the Python Script**:
   ```bash
   cd /home/<USER>/odooProjects/odoo17/custom_addons_develop_myself/tradingview_clone
   python test_data/import_test_data.py
   ```

2. **Or from Odoo Shell**:
   ```python
   exec(open('test_data/import_test_data.py').read())
   ```

## 🧪 Testing the Imported Data

### 1. **Test Symbol Management**
- Navigate to **TradingView → Symbols**
- Verify 30 symbols are imported
- Test search and filtering functionality
- Check different asset types (crypto, stocks, forex, commodities, indices)

### 2. **Test OHLC Data**
- Navigate to **TradingView → OHLC Data**
- Filter by symbol (e.g., BTCUSD)
- Check different timeframes (1D, 1H, 15M)
- Verify computed fields (daily_change, daily_change_percent)

### 3. **Test Technical Indicators**
- Navigate to **TradingView → Technical Indicators**
- Check RSI, MACD, SMA values
- Verify buy/sell/hold signals
- Test filtering by symbol and indicator type

### 4. **Test News Articles**
- Navigate to **TradingView → News**
- Test sentiment filtering
- Check the `is_recent` computed field
- Verify symbol associations

### 5. **Test Events**
- Navigate to **TradingView → Events**
- Check upcoming vs. historical events
- Test event type filtering
- Verify `is_upcoming` computed field

### 6. **Test Watchlists**
- Navigate to **TradingView → Watchlist**
- Check different categories
- Verify symbol associations
- Test performance calculations

### 7. **Test Sync Logs (Critical - Tests Our Fixes)**
- Navigate to **TradingView → Sync Logs**
- **Test "Recent" filter** - should show syncs from last 24 hours
- **Test "Rate Limited" filter** - should show syncs with rate_limit_hit=True
- **Test "With Errors" filter** - should show syncs with error messages
- Verify all search filters work without errors

## 🔍 Data Verification Queries

Use these queries in Odoo shell to verify data integrity:

```python
# Check symbol count by type
for symbol_type in ['stock', 'crypto', 'forex', 'commodity', 'index']:
    count = env['tradingview.symbol'].search_count([('type', '=', symbol_type)])
    print(f"{symbol_type.title()}: {count} symbols")

# Check recent sync logs (tests our fix)
recent_syncs = env['tradingview.sync_log'].search([('is_recent', '=', True)])
print(f"Recent syncs (last 24h): {len(recent_syncs)}")

# Check rate limited syncs
rate_limited = env['tradingview.sync_log'].search([('rate_limit_hit', '=', True)])
print(f"Rate limited syncs: {len(rate_limited)}")

# Check syncs with errors
error_syncs = env['tradingview.sync_log'].search([('error_message', '!=', False)])
print(f"Syncs with errors: {len(error_syncs)}")

# Check news sentiment distribution
for sentiment in ['positive', 'negative', 'neutral']:
    count = env['tradingview.news'].search_count([('sentiment', '=', sentiment)])
    print(f"{sentiment.title()} news: {count}")

# Check upcoming events
upcoming = env['tradingview.event'].search_count([('is_upcoming', '=', True)])
print(f"Upcoming events: {upcoming}")
```

## 🎯 Key Testing Scenarios

### 1. **Search Filter Testing (Critical)**
The most important test is verifying that the search filters we fixed work correctly:

- **Recent Filter**: Should show only sync logs from the last 24 hours
- **Rate Limited Filter**: Should show only syncs where rate_limit_hit=True
- **With Errors Filter**: Should show only syncs with non-empty error_message

### 2. **Computed Field Testing**
- Verify `is_recent` field calculation in sync logs
- Check `daily_change` and `daily_change_percent` in OHLC data
- Test `is_upcoming` field in events

### 3. **Relationship Testing**
- Verify symbol-to-news associations
- Check symbol-to-watchlist relationships
- Test symbol-to-technical-indicator links

## 📝 Notes

- **Data is marked with `noupdate="1"`** to prevent overwriting during module updates
- **All timestamps use UTC timezone**
- **Prices and values are realistic** but not real-time market data
- **External IDs are provided** for easy reference and updates
- **Data includes both successful and failed scenarios** for comprehensive testing

## 🔧 Troubleshooting

### Common Issues:

1. **Import Order**: Always import symbols first, as other models reference them
2. **Database Constraints**: Ensure the TradingView Clone module is properly installed
3. **Permissions**: Make sure you have admin rights to import data
4. **File Paths**: Verify XML files are in the correct directory

### Error Resolution:

- **Foreign Key Errors**: Import symbols before other data
- **Validation Errors**: Check field constraints in model definitions
- **Permission Errors**: Run imports as admin user
- **XML Parsing Errors**: Validate XML syntax

This test data provides a comprehensive foundation for testing all aspects of the TradingView Clone module and demonstrates its capabilities effectively.
