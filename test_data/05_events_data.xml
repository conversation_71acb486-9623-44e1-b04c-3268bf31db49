<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Apple Events -->
        <record id="event_aapl_earnings_q1" model="tradingview.event">
            <field name="title">Apple Q1 2024 Earnings Report</field>
            <field name="description">Apple Inc. will report its first quarter 2024 financial results, including iPhone sales, services revenue, and guidance for the next quarter.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-01-25 16:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_aapl')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_aapl_dividend" model="tradingview.event">
            <field name="title">Apple Quarterly Dividend Payment</field>
            <field name="description">Apple will pay its quarterly dividend of $0.24 per share to shareholders of record.</field>
            <field name="event_type">dividend</field>
            <field name="event_date">2024-02-15 00:00:00</field>
            <field name="impact">low</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_aapl')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Microsoft Events -->
        <record id="event_msft_earnings_q2" model="tradingview.event">
            <field name="title">Microsoft Q2 2024 Earnings Report</field>
            <field name="description">Microsoft Corporation will announce its second quarter 2024 financial results, with focus on Azure cloud growth and AI initiatives.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-01-24 17:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_msft')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Tesla Events -->
        <record id="event_tsla_earnings_q4" model="tradingview.event">
            <field name="title">Tesla Q4 2023 Earnings Report</field>
            <field name="description">Tesla will report fourth quarter 2023 results, including vehicle delivery numbers and production guidance for 2024.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-01-24 17:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_tsla')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_tsla_product_launch" model="tradingview.event">
            <field name="title">Tesla Cybertruck Delivery Event</field>
            <field name="description">Tesla will host a delivery event for the highly anticipated Cybertruck, marking the beginning of mass production.</field>
            <field name="event_type">product_launch</field>
            <field name="event_date">2024-02-10 20:00:00</field>
            <field name="impact">medium</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_tsla')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- NVIDIA Events -->
        <record id="event_nvda_earnings_q4" model="tradingview.event">
            <field name="title">NVIDIA Q4 2024 Earnings Report</field>
            <field name="description">NVIDIA will report fourth quarter 2024 financial results, with particular focus on AI chip sales and data center revenue.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-02-21 16:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_nvda')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_nvda_conference" model="tradingview.event">
            <field name="title">NVIDIA GTC 2024 Conference</field>
            <field name="description">NVIDIA's annual GPU Technology Conference featuring announcements about new AI technologies and partnerships.</field>
            <field name="event_type">conference</field>
            <field name="event_date">2024-03-18 09:00:00</field>
            <field name="impact">medium</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_nvda')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Amazon Events -->
        <record id="event_amzn_earnings_q4" model="tradingview.event">
            <field name="title">Amazon Q4 2023 Earnings Report</field>
            <field name="description">Amazon will report fourth quarter 2023 results, including AWS growth, retail performance, and advertising revenue.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-02-01 16:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_amzn')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Meta Events -->
        <record id="event_meta_earnings_q4" model="tradingview.event">
            <field name="title">Meta Q4 2023 Earnings Report</field>
            <field name="description">Meta Platforms will announce fourth quarter 2023 results, with focus on user growth, metaverse investments, and advertising revenue.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-02-07 16:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_meta')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Netflix Events -->
        <record id="event_nflx_earnings_q4" model="tradingview.event">
            <field name="title">Netflix Q4 2023 Earnings Report</field>
            <field name="description">Netflix will report fourth quarter 2023 results, including subscriber numbers, content spending, and international expansion updates.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2024-01-23 16:00:00</field>
            <field name="impact">medium</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_nflx')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Economic Events -->
        <record id="event_fed_meeting" model="tradingview.event">
            <field name="title">Federal Reserve FOMC Meeting</field>
            <field name="description">The Federal Open Market Committee will meet to discuss monetary policy and potential interest rate changes.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2024-01-31 14:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_qqq'), ref('symbol_dji')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_nonfarm_payrolls" model="tradingview.event">
            <field name="title">US Non-Farm Payrolls Report</field>
            <field name="description">The Bureau of Labor Statistics will release the monthly employment report, including job creation numbers and unemployment rate.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2024-02-02 08:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_eurusd'), ref('symbol_gold')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_cpi_report" model="tradingview.event">
            <field name="title">US Consumer Price Index (CPI) Report</field>
            <field name="description">The Bureau of Labor Statistics will release the monthly inflation report, showing changes in consumer prices.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2024-02-13 08:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_gold'), ref('symbol_btcusd')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_ecb_meeting" model="tradingview.event">
            <field name="title">European Central Bank Policy Meeting</field>
            <field name="description">The European Central Bank will announce its monetary policy decision and interest rate changes for the Eurozone.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2024-02-08 12:45:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_eurusd'), ref('symbol_gbpusd')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Cryptocurrency Events -->
        <record id="event_btc_halving" model="tradingview.event">
            <field name="title">Bitcoin Halving Event</field>
            <field name="description">The Bitcoin network will undergo its next halving event, reducing the block reward from 6.25 to 3.125 BTC.</field>
            <field name="event_type">network_upgrade</field>
            <field name="event_date">2024-04-20 12:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_btcusd')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <record id="event_eth_upgrade" model="tradingview.event">
            <field name="title">Ethereum Dencun Upgrade</field>
            <field name="description">Ethereum will implement the Dencun upgrade, introducing proto-danksharding to improve scalability and reduce transaction costs.</field>
            <field name="event_type">network_upgrade</field>
            <field name="event_date">2024-03-13 12:00:00</field>
            <field name="impact">medium</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_ethusd')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Commodity Events -->
        <record id="event_opec_meeting" model="tradingview.event">
            <field name="title">OPEC+ Ministerial Meeting</field>
            <field name="description">OPEC+ members will meet to discuss oil production quotas and market conditions for the coming months.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2024-03-03 10:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_oil')])]"/>
            <field name="is_upcoming">True</field>
        </record>

        <!-- Historical Events (Past) -->
        <record id="event_aapl_earnings_q4_past" model="tradingview.event">
            <field name="title">Apple Q4 2023 Earnings Report (Past)</field>
            <field name="description">Apple reported strong Q4 2023 results with iPhone sales exceeding expectations.</field>
            <field name="event_type">earnings</field>
            <field name="event_date">2023-11-02 16:30:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_aapl')])]"/>
            <field name="is_upcoming">False</field>
        </record>

        <record id="event_fed_rate_hike_past" model="tradingview.event">
            <field name="title">Federal Reserve Rate Hike (Past)</field>
            <field name="description">The Federal Reserve raised interest rates by 0.25% to combat inflation.</field>
            <field name="event_type">economic</field>
            <field name="event_date">2023-12-13 14:00:00</field>
            <field name="impact">high</field>
            <field name="symbol_ids" eval="[(6, 0, [ref('symbol_spy'), ref('symbol_gold'), ref('symbol_eurusd')])]"/>
            <field name="is_upcoming">False</field>
        </record>

    </data>
</odoo>
