#!/usr/bin/env python3
"""
Direct import script for TradingView Clone module
Run this with: python test_data/direct_import.py
"""

import sys
import os

# Add Odoo to Python path
sys.path.append('/home/<USER>/odooProjects/odoo17/odoo')

import odoo
from odoo import api, SUPERUSER_ID
from datetime import datetime, timedelta

def main():
    print("🚀 Starting TradingView Clone data import...")
    
    # Initialize Odoo
    odoo.tools.config.parse_config([
        '--database=odoo17_tradingview_clone',
        '--addons-path=/home/<USER>/odooProjects/odoo17/odoo/addons,/home/<USER>/odooProjects/odoo17/custom_addons_develop_myself'
    ])
    
    # Get registry
    registry = odoo.registry('odoo17_tradingview_clone')
    
    with registry.cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Create symbols data
        symbols_data = [
            {
                'symbol': 'BTCUSD',
                'name': 'Bitcoin / US Dollar',
                'type': 'crypto',
                'exchange': 'Binance',
                'currency': 'USD',
                'current_price': 43250.50,
                'daily_change': 1250.30,
                'daily_change_percent': 2.98,
                'volume': 2850000000,
                'market_cap': 847500000000,
                'description': 'Bitcoin is the world\'s first and largest cryptocurrency by market capitalization.',
            },
            {
                'symbol': 'ETHUSD',
                'name': 'Ethereum / US Dollar',
                'type': 'crypto',
                'exchange': 'Binance',
                'currency': 'USD',
                'current_price': 2650.75,
                'daily_change': -85.25,
                'daily_change_percent': -3.11,
                'volume': 1250000000,
                'market_cap': 318750000000,
                'description': 'Ethereum is a decentralized platform for smart contracts and decentralized applications.',
            },
            {
                'symbol': 'AAPL',
                'name': 'Apple Inc.',
                'type': 'stock',
                'exchange': 'NASDAQ',
                'currency': 'USD',
                'current_price': 185.92,
                'daily_change': 2.15,
                'daily_change_percent': 1.17,
                'volume': 52000000,
                'market_cap': 2890000000000,
                'description': 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories.',
            },
            {
                'symbol': 'MSFT',
                'name': 'Microsoft Corporation',
                'type': 'stock',
                'exchange': 'NASDAQ',
                'currency': 'USD',
                'current_price': 378.85,
                'daily_change': -5.42,
                'daily_change_percent': -1.41,
                'volume': 28500000,
                'market_cap': 2815000000000,
                'description': 'Microsoft Corporation develops, licenses, and supports software, services, devices, and solutions worldwide.',
            },
            {
                'symbol': 'TSLA',
                'name': 'Tesla, Inc.',
                'type': 'stock',
                'exchange': 'NASDAQ',
                'currency': 'USD',
                'current_price': 248.42,
                'daily_change': 12.85,
                'daily_change_percent': 5.45,
                'volume': 89500000,
                'market_cap': 789000000000,
                'description': 'Tesla, Inc. designs, develops, manufactures, leases, and sells electric vehicles, and energy generation and storage systems.',
            },
            {
                'symbol': 'EURUSD',
                'name': 'Euro / US Dollar',
                'type': 'forex',
                'exchange': 'Forex',
                'currency': 'USD',
                'current_price': 1.0875,
                'daily_change': 0.0025,
                'daily_change_percent': 0.23,
                'volume': 1850000000,
                'description': 'The Euro against the US Dollar is the most traded currency pair in the world.',
            },
            {
                'symbol': 'XAUUSD',
                'name': 'Gold / US Dollar',
                'type': 'commodity',
                'exchange': 'COMEX',
                'currency': 'USD',
                'current_price': 2045.50,
                'daily_change': 15.25,
                'daily_change_percent': 0.75,
                'volume': 125000000,
                'description': 'Gold is a precious metal and store of value traded globally.',
            },
            {
                'symbol': 'SPY',
                'name': 'SPDR S&P 500 ETF Trust',
                'type': 'index',
                'exchange': 'NYSE',
                'currency': 'USD',
                'current_price': 485.75,
                'daily_change': 3.25,
                'daily_change_percent': 0.67,
                'volume': 68500000,
                'market_cap': 445000000000,
                'description': 'The SPDR S&P 500 ETF Trust tracks the S&P 500 Index.',
            },
        ]
        
        # Create symbols
        print("📊 Creating symbols...")
        symbol_model = env['tradingview.symbol']
        created_symbols = {}
        
        for symbol_data in symbols_data:
            try:
                symbol = symbol_model.create(symbol_data)
                created_symbols[symbol_data['symbol']] = symbol
                print(f"✅ Created symbol: {symbol_data['symbol']}")
            except Exception as e:
                print(f"❌ Error creating symbol {symbol_data['symbol']}: {str(e)}")
        
        # Create sync logs to test our fixed search filters
        print("🔄 Creating sync logs...")
        sync_log_model = env['tradingview.sync_log']
        
        # Recent sync logs (last 24 hours)
        recent_syncs = [
            {
                'sync_type': 'price_update',
                'api_source': 'Alpha Vantage',
                'start_time': datetime.now() - timedelta(hours=2),
                'end_time': datetime.now() - timedelta(hours=2) + timedelta(minutes=2),
                'status': 'completed',
                'records_processed': 150,
                'records_updated': 148,
                'records_failed': 2,
                'rate_limit_hit': False,
                'error_message': '',
            },
            {
                'sync_type': 'crypto_prices',
                'api_source': 'CoinGecko',
                'start_time': datetime.now() - timedelta(hours=1),
                'end_time': datetime.now() - timedelta(hours=1) + timedelta(minutes=1),
                'status': 'failed',
                'records_processed': 0,
                'records_updated': 0,
                'records_failed': 0,
                'rate_limit_hit': True,
                'error_message': 'API rate limit exceeded. Please try again later.',
            },
            {
                'sync_type': 'news_feed',
                'api_source': 'NewsAPI',
                'start_time': datetime.now() - timedelta(minutes=30),
                'end_time': datetime.now() - timedelta(minutes=28),
                'status': 'partial',
                'records_processed': 50,
                'records_updated': 45,
                'records_failed': 5,
                'rate_limit_hit': False,
                'error_message': 'Some articles failed to process due to invalid data',
            },
        ]
        
        # Old sync logs (more than 24 hours ago)
        old_syncs = [
            {
                'sync_type': 'price_update',
                'api_source': 'Alpha Vantage',
                'start_time': datetime.now() - timedelta(days=2),
                'end_time': datetime.now() - timedelta(days=2) + timedelta(minutes=3),
                'status': 'completed',
                'records_processed': 200,
                'records_updated': 195,
                'records_failed': 5,
                'rate_limit_hit': False,
                'error_message': '',
            },
        ]
        
        all_syncs = recent_syncs + old_syncs
        sync_count = 0
        
        for sync_data in all_syncs:
            try:
                sync_log_model.create(sync_data)
                sync_count += 1
                print(f"✅ Created sync log: {sync_data['sync_type']} - {sync_data['status']}")
            except Exception as e:
                print(f"❌ Error creating sync log: {str(e)}")
        
        # Commit all changes
        cr.commit()
        
        # Final verification
        print("\n🔍 Verifying imported data...")
        symbol_count = env['tradingview.symbol'].search_count([])
        sync_log_count = env['tradingview.sync_log'].search_count([])
        
        print(f"📊 Data Import Summary:")
        print(f"   Symbols: {symbol_count}")
        print(f"   Sync Logs: {sync_log_count}")
        
        # Test the critical search filters we fixed
        recent_syncs = env['tradingview.sync_log'].search([('is_recent', '=', True)])
        rate_limited_syncs = env['tradingview.sync_log'].search([('rate_limit_hit', '=', True)])
        error_syncs = env['tradingview.sync_log'].search([('error_message', '!=', False)])
        
        print(f"\n🔧 Search Filter Tests:")
        print(f"   Recent syncs (last 24h): {len(recent_syncs)}")
        print(f"   Rate limited syncs: {len(rate_limited_syncs)}")
        print(f"   Syncs with errors: {len(error_syncs)}")
        
        if len(recent_syncs) > 0 and len(rate_limited_syncs) > 0 and len(error_syncs) > 0:
            print("✅ All search filters working correctly!")
        else:
            print("⚠️  Some search filters may need verification")
        
        print("\n🎉 Test data import completed successfully!")
        print("\n🎯 Next Steps:")
        print("1. Access your Odoo instance")
        print("2. Navigate to TradingView → Symbols to see imported data")
        print("3. Test the Sync Logs search filters (Recent, Rate Limited, With Errors)")
        print("4. Explore different views (Tree, Form, Kanban, Graph, Pivot)")

if __name__ == '__main__':
    main()
