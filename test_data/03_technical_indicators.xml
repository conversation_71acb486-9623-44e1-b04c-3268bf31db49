<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- BTCUSD Technical Indicators -->
        <record id="tech_btcusd_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">65.75</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.65</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_btcusd_macd" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">macd</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">850.25</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.78</field>
            <field name="parameters">{"fast": 12, "slow": 26, "signal": 9}</field>
        </record>

        <record id="tech_btcusd_sma20" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">sma</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">42150.75</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.72</field>
            <field name="parameters">{"period": 20}</field>
        </record>

        <record id="tech_btcusd_ema50" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">ema</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">41850.25</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.68</field>
            <field name="parameters">{"period": 50}</field>
        </record>

        <record id="tech_btcusd_bb" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">bollinger_bands</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">42500.00</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.55</field>
            <field name="parameters">{"period": 20, "std_dev": 2}</field>
        </record>

        <!-- AAPL Technical Indicators -->
        <record id="tech_aapl_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">58.25</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.58</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_aapl_macd" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="indicator_type">macd</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">2.15</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.75</field>
            <field name="parameters">{"fast": 12, "slow": 26, "signal": 9}</field>
        </record>

        <record id="tech_aapl_sma20" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="indicator_type">sma</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">182.50</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.70</field>
            <field name="parameters">{"period": 20}</field>
        </record>

        <!-- EURUSD Technical Indicators -->
        <record id="tech_eurusd_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_eurusd"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">52.80</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.53</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_eurusd_macd" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_eurusd"/>
            <field name="indicator_type">macd</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">0.0015</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.62</field>
            <field name="parameters">{"fast": 12, "slow": 26, "signal": 9}</field>
        </record>

        <!-- GOLD Technical Indicators -->
        <record id="tech_gold_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_gold"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">68.50</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.69</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_gold_sma50" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_gold"/>
            <field name="indicator_type">sma</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">2025.75</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.74</field>
            <field name="parameters">{"period": 50}</field>
        </record>

        <!-- ETHUSD Technical Indicators -->
        <record id="tech_ethusd_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_ethusd"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">45.25</field>
            <field name="signal">sell</field>
            <field name="signal_strength">0.45</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_ethusd_macd" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_ethusd"/>
            <field name="indicator_type">macd</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">-25.50</field>
            <field name="signal">sell</field>
            <field name="signal_strength">0.65</field>
            <field name="parameters">{"fast": 12, "slow": 26, "signal": 9}</field>
        </record>

        <!-- SPY Technical Indicators -->
        <record id="tech_spy_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_spy"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">62.75</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.63</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_spy_sma200" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_spy"/>
            <field name="indicator_type">sma</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">465.25</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.78</field>
            <field name="parameters">{"period": 200}</field>
        </record>

        <!-- TSLA Technical Indicators -->
        <record id="tech_tsla_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_tsla"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">72.50</field>
            <field name="signal">sell</field>
            <field name="signal_strength">0.73</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_tsla_macd" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_tsla"/>
            <field name="indicator_type">macd</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">8.75</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.82</field>
            <field name="parameters">{"fast": 12, "slow": 26, "signal": 9}</field>
        </record>

        <!-- NVDA Technical Indicators -->
        <record id="tech_nvda_rsi" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_nvda"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">75.25</field>
            <field name="signal">sell</field>
            <field name="signal_strength">0.75</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_nvda_sma20" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_nvda"/>
            <field name="indicator_type">sma</field>
            <field name="timeframe">1D</field>
            <field name="datetime">2024-01-03 00:00:00</field>
            <field name="value">825.50</field>
            <field name="signal">buy</field>
            <field name="signal_strength">0.80</field>
            <field name="parameters">{"period": 20}</field>
        </record>

        <!-- Additional indicators for different timeframes -->
        <record id="tech_btcusd_rsi_1h" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_btcusd"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1H</field>
            <field name="datetime">2024-01-03 15:00:00</field>
            <field name="value">58.25</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.58</field>
            <field name="parameters">{"period": 14}</field>
        </record>

        <record id="tech_aapl_rsi_1h" model="tradingview.technical">
            <field name="symbol_id" ref="symbol_aapl"/>
            <field name="indicator_type">rsi</field>
            <field name="timeframe">1H</field>
            <field name="datetime">2024-01-03 15:00:00</field>
            <field name="value">62.80</field>
            <field name="signal">hold</field>
            <field name="signal_strength">0.63</field>
            <field name="parameters">{"period": 14}</field>
        </record>

    </data>
</odoo>
