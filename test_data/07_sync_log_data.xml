<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Recent Successful Syncs (Last 24 hours) -->
        <record id="sync_log_recent_1" model="tradingview.sync_log">
            <field name="sync_type">price_update</field>
            <field name="api_source">Alpha Vantage</field>
            <field name="start_time">2024-01-03 14:30:00</field>
            <field name="end_time">2024-01-03 14:32:15</field>
            <field name="status">completed</field>
            <field name="records_processed">150</field>
            <field name="records_updated">148</field>
            <field name="records_failed">2</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"symbols_updated": ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"], "api_calls": 5, "rate_limit_remaining": 495}</field>
        </record>

        <record id="sync_log_recent_2" model="tradingview.sync_log">
            <field name="sync_type">crypto_prices</field>
            <field name="api_source">CoinGecko</field>
            <field name="start_time">2024-01-03 13:15:00</field>
            <field name="end_time">2024-01-03 13:16:45</field>
            <field name="status">completed</field>
            <field name="records_processed">25</field>
            <field name="records_updated">25</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"cryptocurrencies": ["BTC", "ETH", "ADA", "SOL", "DOT"], "prices_updated": 25}</field>
        </record>

        <record id="sync_log_recent_3" model="tradingview.sync_log">
            <field name="sync_type">forex_rates</field>
            <field name="api_source">Fixer.io</field>
            <field name="start_time">2024-01-03 12:00:00</field>
            <field name="end_time">2024-01-03 12:01:30</field>
            <field name="status">completed</field>
            <field name="records_processed">8</field>
            <field name="records_updated">8</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"currency_pairs": ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"], "rates_updated": 8}</field>
        </record>

        <record id="sync_log_recent_4" model="tradingview.sync_log">
            <field name="sync_type">news_feed</field>
            <field name="api_source">NewsAPI</field>
            <field name="start_time">2024-01-03 11:45:00</field>
            <field name="end_time">2024-01-03 11:47:20</field>
            <field name="status">completed</field>
            <field name="records_processed">50</field>
            <field name="records_updated">45</field>
            <field name="records_failed">5</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"articles_fetched": 50, "articles_saved": 45, "duplicates_skipped": 5}</field>
        </record>

        <!-- Rate Limited Syncs -->
        <record id="sync_log_rate_limited_1" model="tradingview.sync_log">
            <field name="sync_type">technical_indicators</field>
            <field name="api_source">Alpha Vantage</field>
            <field name="start_time">2024-01-03 10:30:00</field>
            <field name="end_time">2024-01-03 10:30:05</field>
            <field name="status">failed</field>
            <field name="records_processed">0</field>
            <field name="records_updated">0</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">True</field>
            <field name="error_message">API rate limit exceeded. Please try again later.</field>
            <field name="response_data">{"error": "rate_limit_exceeded", "retry_after": 3600, "calls_remaining": 0}</field>
        </record>

        <record id="sync_log_rate_limited_2" model="tradingview.sync_log">
            <field name="sync_type">price_update</field>
            <field name="api_source">IEX Cloud</field>
            <field name="start_time">2024-01-03 09:15:00</field>
            <field name="end_time">2024-01-03 09:15:02</field>
            <field name="status">failed</field>
            <field name="records_processed">0</field>
            <field name="records_updated">0</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">True</field>
            <field name="error_message">Monthly API quota exceeded</field>
            <field name="response_data">{"error": "quota_exceeded", "quota_reset": "2024-02-01T00:00:00Z"}</field>
        </record>

        <!-- Syncs with Errors -->
        <record id="sync_log_with_errors_1" model="tradingview.sync_log">
            <field name="sync_type">commodity_prices</field>
            <field name="api_source">Metals API</field>
            <field name="start_time">2024-01-03 08:45:00</field>
            <field name="end_time">2024-01-03 08:46:30</field>
            <field name="status">partial</field>
            <field name="records_processed">10</field>
            <field name="records_updated">7</field>
            <field name="records_failed">3</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message">Failed to update SILVER, COPPER, PLATINUM prices due to invalid API response</field>
            <field name="response_data">{"successful": ["GOLD", "PALLADIUM"], "failed": ["SILVER", "COPPER", "PLATINUM"], "errors": ["invalid_symbol", "no_data", "timeout"]}</field>
        </record>

        <record id="sync_log_with_errors_2" model="tradingview.sync_log">
            <field name="sync_type">earnings_calendar</field>
            <field name="api_source">Financial Modeling Prep</field>
            <field name="start_time">2024-01-03 07:30:00</field>
            <field name="end_time">2024-01-03 07:32:45</field>
            <field name="status">failed</field>
            <field name="records_processed">0</field>
            <field name="records_updated">0</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message">Authentication failed: Invalid API key</field>
            <field name="response_data">{"error": "authentication_failed", "message": "Invalid API key provided"}</field>
        </record>

        <!-- Older Syncs (Not Recent - More than 24 hours ago) -->
        <record id="sync_log_old_1" model="tradingview.sync_log">
            <field name="sync_type">price_update</field>
            <field name="api_source">Alpha Vantage</field>
            <field name="start_time">2024-01-01 14:30:00</field>
            <field name="end_time">2024-01-01 14:32:15</field>
            <field name="status">completed</field>
            <field name="records_processed">200</field>
            <field name="records_updated">195</field>
            <field name="records_failed">5</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"symbols_updated": ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN", "META"], "api_calls": 7}</field>
        </record>

        <record id="sync_log_old_2" model="tradingview.sync_log">
            <field name="sync_type">crypto_prices</field>
            <field name="api_source">CoinGecko</field>
            <field name="start_time">2023-12-30 16:00:00</field>
            <field name="end_time">2023-12-30 16:02:30</field>
            <field name="status">completed</field>
            <field name="records_processed">30</field>
            <field name="records_updated">30</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"cryptocurrencies": ["BTC", "ETH", "ADA", "SOL", "DOT", "LINK", "UNI"], "prices_updated": 30}</field>
        </record>

        <!-- Manual Syncs -->
        <record id="sync_log_manual_1" model="tradingview.sync_log">
            <field name="sync_type">manual_update</field>
            <field name="api_source">Manual Entry</field>
            <field name="start_time">2024-01-03 16:00:00</field>
            <field name="end_time">2024-01-03 16:05:00</field>
            <field name="status">completed</field>
            <field name="records_processed">5</field>
            <field name="records_updated">5</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"manual_entries": ["BTCUSD", "ETHUSD", "AAPL", "TSLA", "NVDA"], "updated_by": "admin"}</field>
        </record>

        <!-- Scheduled Syncs -->
        <record id="sync_log_scheduled_1" model="tradingview.sync_log">
            <field name="sync_type">scheduled_update</field>
            <field name="api_source">Multiple APIs</field>
            <field name="start_time">2024-01-03 06:00:00</field>
            <field name="end_time">2024-01-03 06:15:30</field>
            <field name="status">completed</field>
            <field name="records_processed">500</field>
            <field name="records_updated">485</field>
            <field name="records_failed">15</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"apis_called": ["Alpha Vantage", "CoinGecko", "Fixer.io", "NewsAPI"], "total_updates": 485}</field>
        </record>

        <!-- Failed Syncs -->
        <record id="sync_log_failed_1" model="tradingview.sync_log">
            <field name="sync_type">ohlc_data</field>
            <field name="api_source">Polygon.io</field>
            <field name="start_time">2024-01-02 20:30:00</field>
            <field name="end_time">2024-01-02 20:30:10</field>
            <field name="status">failed</field>
            <field name="records_processed">0</field>
            <field name="records_updated">0</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message">Connection timeout: Unable to connect to API server</field>
            <field name="response_data">{"error": "connection_timeout", "timeout_duration": 10}</field>
        </record>

        <!-- Partial Success Syncs -->
        <record id="sync_log_partial_1" model="tradingview.sync_log">
            <field name="sync_type">market_data</field>
            <field name="api_source">Yahoo Finance</field>
            <field name="start_time">2024-01-03 15:30:00</field>
            <field name="end_time">2024-01-03 15:33:45</field>
            <field name="status">partial</field>
            <field name="records_processed">100</field>
            <field name="records_updated">85</field>
            <field name="records_failed">15</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message">Some symbols returned invalid data or were not found</field>
            <field name="response_data">{"successful_symbols": 85, "failed_symbols": 15, "invalid_responses": ["INVALID1", "INVALID2"]}</field>
        </record>

        <!-- Long Running Sync -->
        <record id="sync_log_long_running" model="tradingview.sync_log">
            <field name="sync_type">historical_data</field>
            <field name="api_source">Alpha Vantage</field>
            <field name="start_time">2024-01-03 02:00:00</field>
            <field name="end_time">2024-01-03 02:45:30</field>
            <field name="status">completed</field>
            <field name="records_processed">5000</field>
            <field name="records_updated">4950</field>
            <field name="records_failed">50</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"historical_periods": "5 years", "symbols": 50, "data_points": 5000, "api_calls": 250}</field>
        </record>

        <!-- Recent Error with Rate Limit -->
        <record id="sync_log_recent_error_rate" model="tradingview.sync_log">
            <field name="sync_type">real_time_quotes</field>
            <field name="api_source">IEX Cloud</field>
            <field name="start_time">2024-01-03 17:45:00</field>
            <field name="end_time">2024-01-03 17:45:03</field>
            <field name="status">failed</field>
            <field name="records_processed">0</field>
            <field name="records_updated">0</field>
            <field name="records_failed">0</field>
            <field name="rate_limit_hit">True</field>
            <field name="error_message">Rate limit exceeded: 1000 requests per minute limit reached</field>
            <field name="response_data">{"error": "rate_limit_exceeded", "limit": 1000, "window": "1 minute", "retry_after": 60}</field>
        </record>

        <!-- Successful Recent Sync with Large Volume -->
        <record id="sync_log_recent_large" model="tradingview.sync_log">
            <field name="sync_type">bulk_update</field>
            <field name="api_source">Multiple APIs</field>
            <field name="start_time">2024-01-03 18:00:00</field>
            <field name="end_time">2024-01-03 18:12:30</field>
            <field name="status">completed</field>
            <field name="records_processed">1000</field>
            <field name="records_updated">985</field>
            <field name="records_failed">15</field>
            <field name="rate_limit_hit">False</field>
            <field name="error_message"></field>
            <field name="response_data">{"bulk_operation": true, "symbols_updated": 985, "apis_used": 5, "total_api_calls": 200}</field>
        </record>

    </data>
</odoo>
