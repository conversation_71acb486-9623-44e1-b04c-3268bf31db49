#!/usr/bin/env python3
"""
TradingView Clone Module - Test Data Import Script

This script imports comprehensive test data for the TradingView Clone module.
It can be run from the Odoo shell or as a standalone script.

Usage:
1. From Odoo shell:
   python odoo-bin shell -d your_database --addons-path=your_addons_path
   >>> exec(open('test_data/import_test_data.py').read())

2. As standalone script (requires odoo environment):
   python import_test_data.py

The script imports:
- 30 Financial symbols (stocks, crypto, forex, commodities, indices)
- OHLC historical data for multiple timeframes
- Technical indicators with realistic values
- News articles with sentiment analysis
- Financial events (earnings, economic announcements)
- User watchlists with different categories
- Sync logs to test the fixed search functionality
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def import_xml_data(env, xml_file_path, description=""):
    """Import XML data file into Odoo"""
    try:
        if not os.path.exists(xml_file_path):
            logger.error(f"XML file not found: {xml_file_path}")
            return False
            
        logger.info(f"Importing {description}: {xml_file_path}")
        
        # Read XML file
        with open(xml_file_path, 'r', encoding='utf-8') as file:
            xml_content = file.read()
        
        # Import using Odoo's XML parser
        from odoo.tools import convert
        from io import StringIO
        
        # Create a file-like object from the XML content
        xml_file = StringIO(xml_content)
        
        # Import the XML data
        convert.convert_xml_import(
            env, 
            'tradingview_clone',  # module name
            xml_file, 
            {}, 
            'init', 
            False  # noupdate
        )
        
        env.cr.commit()
        logger.info(f"Successfully imported {description}")
        return True
        
    except Exception as e:
        logger.error(f"Error importing {description}: {str(e)}")
        env.cr.rollback()
        return False

def verify_data_import(env):
    """Verify that the test data was imported correctly"""
    logger.info("Verifying imported data...")
    
    try:
        # Check symbols
        symbol_count = env['tradingview.symbol'].search_count([])
        logger.info(f"Symbols imported: {symbol_count}")
        
        # Check OHLC data
        ohlc_count = env['tradingview.ohlc'].search_count([])
        logger.info(f"OHLC records imported: {ohlc_count}")
        
        # Check technical indicators
        tech_count = env['tradingview.technical'].search_count([])
        logger.info(f"Technical indicators imported: {tech_count}")
        
        # Check news articles
        news_count = env['tradingview.news'].search_count([])
        logger.info(f"News articles imported: {news_count}")
        
        # Check events
        event_count = env['tradingview.event'].search_count([])
        logger.info(f"Events imported: {event_count}")
        
        # Check watchlists
        watchlist_count = env['tradingview.watchlist'].search_count([])
        logger.info(f"Watchlists imported: {watchlist_count}")
        
        # Check sync logs
        sync_log_count = env['tradingview.sync_log'].search_count([])
        logger.info(f"Sync logs imported: {sync_log_count}")
        
        # Test the fixed is_recent field
        recent_syncs = env['tradingview.sync_log'].search([('is_recent', '=', True)])
        logger.info(f"Recent sync logs (last 24h): {len(recent_syncs)}")
        
        # Test rate limited syncs
        rate_limited_syncs = env['tradingview.sync_log'].search([('rate_limit_hit', '=', True)])
        logger.info(f"Rate limited syncs: {len(rate_limited_syncs)}")
        
        # Test syncs with errors
        error_syncs = env['tradingview.sync_log'].search([('error_message', '!=', False)])
        logger.info(f"Syncs with errors: {len(error_syncs)}")
        
        logger.info("Data verification completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error during data verification: {str(e)}")
        return False

def main():
    """Main function to import all test data"""
    logger.info("Starting TradingView Clone test data import...")
    
    # Check if we're running in Odoo environment
    try:
        import odoo
        from odoo import api, SUPERUSER_ID
        
        # Get database name from command line or use default
        db_name = 'odoo17_tradingview_clone'  # Change this to your database name
        
        # Initialize Odoo registry
        registry = odoo.registry(db_name)
        
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # Define the data files to import in order
            data_files = [
                ('test_data/01_symbols_data.xml', 'Financial Symbols'),
                ('test_data/02_ohlc_data.xml', 'OHLC Historical Data'),
                ('test_data/03_technical_indicators.xml', 'Technical Indicators'),
                ('test_data/04_news_data.xml', 'News Articles'),
                ('test_data/05_events_data.xml', 'Financial Events'),
                ('test_data/06_watchlist_data.xml', 'Watchlists'),
                ('test_data/07_sync_log_data.xml', 'Sync Logs'),
            ]
            
            # Import each data file
            success_count = 0
            for xml_file, description in data_files:
                if import_xml_data(env, xml_file, description):
                    success_count += 1
                else:
                    logger.error(f"Failed to import {description}")
            
            logger.info(f"Import completed: {success_count}/{len(data_files)} files imported successfully")
            
            # Verify the imported data
            if success_count == len(data_files):
                verify_data_import(env)
            
            logger.info("Test data import process completed!")
            
    except ImportError:
        logger.error("Odoo not found. Please run this script from the Odoo environment.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during import process: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
